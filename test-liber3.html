<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liber3 API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #fff;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        
        .book-item {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .book-title {
            font-weight: bold;
            color: #333;
        }
        
        .book-meta {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 Liber3 API测试工具</h1>
    
    <div class="test-section">
        <h3>基础连接测试</h3>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="testCORS()">测试CORS</button>
        <div id="connection-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>搜索功能测试</h3>
        <input type="text" id="search-keyword" placeholder="输入搜索关键词" value="python" style="padding: 8px; margin-right: 10px;">
        <button onclick="testSearch()">搜索书籍</button>
        <button onclick="clearResults()">清除结果</button>
        <div id="search-result" class="result" style="display: none;"></div>
        <div id="books-list"></div>
    </div>
    
    <div class="test-section">
        <h3>书籍详情测试</h3>
        <input type="text" id="book-id" placeholder="输入书籍ID" style="padding: 8px; margin-right: 10px;">
        <button onclick="testBookDetails()">获取详情</button>
        <div id="details-result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'https://lgate.glitternode.ru/v1';
        
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        function clearResults() {
            document.getElementById('search-result').style.display = 'none';
            document.getElementById('books-list').innerHTML = '';
        }
        
        async function testConnection() {
            log('connection-result', '正在测试连接...');
            
            try {
                const response = await fetch(`${API_BASE}/searchV2`, {
                    method: 'OPTIONS'
                });
                
                log('connection-result', `连接成功! 状态: ${response.status}`);
                
            } catch (error) {
                log('connection-result', `连接失败: ${error.message}`, true);
            }
        }
        
        async function testCORS() {
            log('connection-result', '正在测试CORS...');
            
            try {
                const response = await fetch(`${API_BASE}/searchV2`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        address: '',
                        word: 'test'
                    })
                });
                
                if (response.ok) {
                    log('connection-result', 'CORS测试成功!');
                } else {
                    log('connection-result', `CORS测试失败: ${response.status} ${response.statusText}`, true);
                }
                
            } catch (error) {
                log('connection-result', `CORS测试失败: ${error.message}`, true);
            }
        }
        
        async function testSearch() {
            const keyword = document.getElementById('search-keyword').value.trim();
            if (!keyword) {
                log('search-result', '请输入搜索关键词', true);
                return;
            }
            
            log('search-result', `正在搜索: "${keyword}"`);
            
            try {
                const response = await fetch(`${API_BASE}/searchV2`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        address: '',
                        word: keyword
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('search-result', `搜索成功! 找到 ${data.data?.book?.length || 0} 个结果`);
                
                displayBooks(data.data?.book || []);
                
            } catch (error) {
                log('search-result', `搜索失败: ${error.message}`, true);
            }
        }
        
        function displayBooks(books) {
            const booksList = document.getElementById('books-list');
            booksList.innerHTML = '';
            
            if (books.length === 0) {
                booksList.innerHTML = '<p>没有找到结果</p>';
                return;
            }
            
            books.slice(0, 10).forEach((book, index) => {
                const bookDiv = document.createElement('div');
                bookDiv.className = 'book-item';
                bookDiv.innerHTML = `
                    <div class="book-title">${book.title || '未知标题'}</div>
                    <div class="book-meta">
                        作者: ${book.author || '未知作者'} | 
                        ID: ${book.id} | 
                        <button onclick="testBookDetailsById('${book.id}')" style="font-size: 10px; padding: 2px 6px;">获取详情</button>
                    </div>
                `;
                booksList.appendChild(bookDiv);
            });
        }
        
        async function testBookDetails() {
            const bookId = document.getElementById('book-id').value.trim();
            if (!bookId) {
                log('details-result', '请输入书籍ID', true);
                return;
            }
            
            await testBookDetailsById(bookId);
        }
        
        async function testBookDetailsById(bookId) {
            log('details-result', `正在获取书籍详情: ${bookId}`);
            
            try {
                const response = await fetch(`${API_BASE}/book`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        book_ids: [bookId]
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const bookDetail = data.data?.book?.[bookId]?.book;
                
                if (bookDetail) {
                    const details = `
书籍详情:
标题: ${bookDetail.title || '未知'}
作者: ${bookDetail.author || '未知'}
出版社: ${bookDetail.publisher || '未知'}
年份: ${bookDetail.year || '未知'}
格式: ${bookDetail.extension || '未知'}
文件大小: ${bookDetail.filesize || '未知'}
IPFS CID: ${bookDetail.ipfs_cid || '无'}
语言: ${bookDetail.language || '未知'}
页数: ${bookDetail.pages || '未知'}
                    `;
                    log('details-result', details);
                } else {
                    log('details-result', '未找到书籍详情', true);
                }
                
            } catch (error) {
                log('details-result', `获取详情失败: ${error.message}`, true);
            }
        }
        
        // 页面加载时自动测试连接
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
