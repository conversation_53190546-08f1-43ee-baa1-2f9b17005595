<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TouchFish Reader 快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #fff;
            border: 1px solid #ddd;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .highlight:hover {
            background: #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>📚 TouchFish Reader v2.1.1 快速测试</h1>
    
    <div class="warning">
        <h3>⚠️ 当前状态</h3>
        <p>Ylibrary API可能暂时不可用，已切换到Google Books作为默认搜索源。Google Books可以正常搜索，但不支持直接下载。</p>
    </div>
    
    <div class="test-section">
        <h3>🔍 搜索功能测试</h3>
        <p>测试Google Books API是否正常工作：</p>
        <button onclick="testGoogleBooksAPI()">测试Google Books搜索</button>
        <button onclick="testYlibraryAPI()">测试Ylibrary搜索</button>
        <div id="search-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>📖 插件功能测试</h3>
        <p>测试TouchFish Reader的基本功能：</p>
        <ol>
            <li>点击浏览器工具栏中的📚图标</li>
            <li>确认默认搜索源为"Google Books"</li>
            <li>搜索关键词如"python"或"小说"</li>
            <li>查看搜索结果和分页功能</li>
            <li>尝试切换搜索源</li>
        </ol>
        
        <div class="highlight">
            <strong>测试文本区域</strong><br>
            选择这段文本来测试TouchFish Reader的文本替换功能。首先需要导入一本电子书（本地文件导入），然后激活插件，最后选择这段文本。这段文本应该会被替换为你导入的电子书内容。
        </div>
    </div>
    
    <div class="test-section">
        <h3>📋 功能检查清单</h3>
        <div id="checklist">
            <label><input type="checkbox"> 插件图标显示正常</label><br>
            <label><input type="checkbox"> 弹出窗口打开正常</label><br>
            <label><input type="checkbox"> 搜索源选择器工作正常</label><br>
            <label><input type="checkbox"> Google Books搜索返回结果</label><br>
            <label><input type="checkbox"> 分页控件显示正常</label><br>
            <label><input type="checkbox"> 本地文件导入功能正常</label><br>
            <label><input type="checkbox"> 文本替换功能正常</label><br>
            <label><input type="checkbox"> 快捷键翻页正常</label><br>
        </div>
    </div>
    
    <div class="test-section success" style="display: none;" id="success-message">
        <h3>✅ 测试通过</h3>
        <p>TouchFish Reader v2.1.1 功能正常！虽然Ylibrary暂时不可用，但Google Books搜索和本地导入功能都工作正常。</p>
    </div>

    <script src="book-search-api.js"></script>
    <script>
        let testResults = {
            googleBooks: false,
            ylibrary: false
        };
        
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('search-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
        }
        
        async function testGoogleBooksAPI() {
            showResult('正在测试Google Books API...', true);
            
            try {
                const api = new BookSearchAPI();
                const result = await api.searchGoogleBooks('test', { page: 1, pageSize: 5 });
                
                if (result.results && result.results.length > 0) {
                    testResults.googleBooks = true;
                    showResult(`✅ Google Books API正常！找到 ${result.results.length} 个结果`, true);
                } else {
                    showResult('⚠️ Google Books API返回空结果', false);
                }
            } catch (error) {
                showResult(`❌ Google Books API测试失败: ${error.message}`, false);
            }
        }
        
        async function testYlibraryAPI() {
            showResult('正在测试Ylibrary API...', true);
            
            try {
                const api = new BookSearchAPI();
                const result = await api.searchYlibrary('test', { page: 1, pageSize: 5 });
                
                if (result.results && result.results.length > 0) {
                    testResults.ylibrary = true;
                    showResult(`✅ Ylibrary API正常！找到 ${result.results.length} 个结果`, true);
                } else {
                    showResult('⚠️ Ylibrary API返回空结果', false);
                }
            } catch (error) {
                testResults.ylibrary = false;
                showResult(`❌ Ylibrary API测试失败: ${error.message}`, false);
            }
        }
        
        // 检查清单变化监听
        document.getElementById('checklist').addEventListener('change', function() {
            const checkboxes = this.querySelectorAll('input[type="checkbox"]');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            
            if (checkedCount >= 6) { // 至少6个功能正常
                document.getElementById('success-message').style.display = 'block';
            }
        });
        
        // 页面加载完成后自动测试Google Books
        window.addEventListener('load', () => {
            setTimeout(() => {
                testGoogleBooksAPI();
            }, 1000);
        });
    </script>
</body>
</html>
