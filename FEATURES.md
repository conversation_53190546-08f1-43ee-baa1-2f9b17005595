# TouchFish Reader - 在线搜索功能详细说明

## 🎉 新功能概览

我们为 TouchFish Reader 添加了强大的在线电子书搜索和下载功能，让你可以直接在插件中搜索并导入电子书，无需手动寻找和下载文件。

## 🔍 搜索功能特性

### 多源搜索
- **Ylibrary API**: 去中心化图书馆，包含1500万+中文电子书
- **Google Books API**: Google图书搜索，提供丰富的书籍信息
- **智能聚合**: 自动合并多个搜索源的结果，去重并排序

### 智能筛选
- **格式筛选**: 支持PDF、EPUB、TXT、MOBI、AZW3等格式
- **排序选项**: 按相关度、出版时间、文件大小排序
- **来源标识**: 清晰显示每本书的来源和可用性

### 搜索体验
- **实时搜索**: 输入关键词即可搜索，支持回车快捷搜索
- **结果预览**: 显示书籍标题、作者、出版社、年份等详细信息
- **下载状态**: 实时显示下载进度和状态

## 📥 下载功能

### 支持的下载方式
- **IPFS下载**: 通过多个IPFS网关下载，确保可用性
- **直接下载**: 支持直接HTTP下载链接
- **智能重试**: 自动尝试多个下载源，提高成功率

### 文件处理
- **格式检测**: 自动识别文件格式并选择合适的解析器
- **内容提取**: 支持从PDF、EPUB等复杂格式中提取纯文本
- **编码处理**: 自动处理各种文本编码，确保中文显示正常

### 导入流程
1. 用户选择搜索结果中的书籍
2. 点击"下载导入"按钮
3. 系统自动下载文件
4. 解析文件内容
5. 导入到阅读器
6. 更新书籍列表

## 🎨 界面设计

### 搜索界面
- **搜索框**: 支持书名、作者名搜索
- **筛选器**: 格式和排序选项的下拉菜单
- **搜索按钮**: 一键搜索，支持加载状态显示

### 结果展示
- **卡片式布局**: 每个搜索结果以卡片形式展示
- **信息丰富**: 显示标题、作者、格式、大小、来源等信息
- **操作按钮**: 下载导入、预览等操作按钮
- **进度显示**: 下载时显示实时进度条

### 状态反馈
- **加载动画**: 搜索时显示旋转加载图标
- **成功提示**: 下载成功后显示绿色提示消息
- **错误处理**: 详细的错误信息和解决建议

## 🔧 技术实现

### API模块 (book-search-api.js)
```javascript
class BookSearchAPI {
  // 搜索电子书
  async searchBooks(keyword, options)
  
  // 下载电子书
  async downloadBook(bookInfo, onProgress)
  
  // 获取书籍详情
  async getBookDetails(bookId, source)
}
```

### 主要方法
- `searchYlibrary()`: Ylibrary API搜索
- `searchGoogleBooks()`: Google Books API搜索
- `downloadFromIPFS()`: IPFS下载实现
- `aggregateResults()`: 结果聚合和去重
- `processDownloadedFile()`: 文件格式处理

### 权限配置
```json
{
  "host_permissions": [
    "https://api.ylibrary.org/*",
    "https://www.googleapis.com/*",
    "https://books.google.com/*",
    "https://gateway.ipfs.io/*",
    "https://*.ipfs.io/*"
  ]
}
```

## 📊 使用统计

### 搜索源对比
| 搜索源 | 书籍数量 | 中文书籍 | 下载支持 | 格式支持 |
|--------|----------|----------|----------|----------|
| Ylibrary | 1500万+ | 丰富 | ✅ | PDF/EPUB/TXT/MOBI |
| Google Books | 数千万 | 一般 | 部分 | PDF/EPUB |

### 支持格式
- **PDF**: 最常见，兼容性最好
- **EPUB**: 标准电子书格式，排版优秀
- **TXT**: 纯文本，体积小，加载快
- **MOBI/AZW3**: Kindle格式，部分支持

## 🚀 使用建议

### 搜索技巧
1. **使用准确的书名**: 完整的书名比部分关键词效果更好
2. **尝试作者名**: 如果书名搜索无果，可以尝试作者名
3. **选择合适格式**: PDF通常可用性最高，EPUB排版最好
4. **多尝试几个结果**: 同一本书可能有多个版本

### 下载优化
1. **网络环境**: 确保网络连接稳定
2. **文件大小**: 优先选择适中大小的文件
3. **来源选择**: Ylibrary来源的书籍下载成功率更高
4. **耐心等待**: 大文件下载可能需要一些时间

## 🔒 隐私和安全

### 数据保护
- **本地存储**: 所有书籍内容都存储在本地
- **无上传**: 不会向服务器上传任何个人数据
- **匿名搜索**: 搜索请求不包含个人标识信息

### 版权声明
- **合法使用**: 请确保下载的书籍符合当地法律法规
- **个人使用**: 建议仅用于个人学习和研究
- **尊重版权**: 支持正版，尊重作者权益

## 🐛 故障排除

### 常见问题
1. **搜索无结果**: 检查网络连接，尝试不同关键词
2. **下载失败**: 尝试其他格式或来源的书籍
3. **文件无法解析**: 可能是文件损坏或格式不支持
4. **导入失败**: 检查文件大小，确保不超过100MB

### 解决方案
- 刷新页面重试
- 检查网络连接
- 尝试不同的搜索关键词
- 选择其他格式的书籍
- 联系开发者反馈问题

## 🔮 未来计划

### 即将推出
- [ ] 更多电子书搜索源
- [ ] 智能推荐系统
- [ ] 书籍评分和评论
- [ ] 云端同步功能
- [ ] 社交分享功能

### 长期规划
- [ ] AI智能摘要
- [ ] 多语言支持
- [ ] 语音朗读功能
- [ ] 个性化主题
- [ ] 阅读统计分析

---

感谢使用 TouchFish Reader！如有问题或建议，欢迎反馈。
