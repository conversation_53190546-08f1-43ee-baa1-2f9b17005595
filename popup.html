<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      margin: 0;
      background: #f8f9fa;
    }
    
    .header {
      text-align: center;
      margin-bottom: 15px;
      color: #333;
    }
    
    .status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      background: white;
      border-radius: 6px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .status-text {
      font-weight: 500;
    }
    
    .toggle-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s;
    }
    
    .toggle-btn.active {
      background: #dc3545;
      color: white;
    }
    
    .toggle-btn.inactive {
      background: #28a745;
      color: white;
    }
    
    .toggle-btn:hover {
      opacity: 0.8;
    }
    
    .book-section {
      background: white;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .section-title {
      font-weight: 600;
      margin-bottom: 10px;
      color: #333;
    }
    
    .file-input-wrapper {
      position: relative;
      display: inline-block;
      width: 100%;
    }
    
    .file-input {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
    
    .file-input-btn {
      display: block;
      width: 100%;
      padding: 10px;
      background: #007bff;
      color: white;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.2s;
    }
    
    .file-input-btn:hover {
      background: #0056b3;
    }
    
    .progress-info {
      background: white;
      padding: 10px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      margin-bottom: 15px;
    }
    
    .progress-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 13px;
    }
    
    .instructions {
      background: #e9ecef;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.4;
      color: #666;
    }
    
    .instructions h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 13px;
    }
    
    .instructions ul {
      margin: 0;
      padding-left: 16px;
    }
    
    .instructions li {
      margin-bottom: 4px;
    }
    
    .info-section {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }

    .info-title {
      font-weight: 600;
      color: #155724;
      margin-bottom: 5px;
      font-size: 12px;
    }

    .info-item {
      font-size: 11px;
      color: #155724;
      margin-bottom: 2px;
    }

    .todo-section {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }

    .todo-title {
      font-weight: 600;
      color: #856404;
      margin-bottom: 5px;
      font-size: 12px;
    }

    .todo-item {
      font-size: 11px;
      color: #856404;
      margin-bottom: 2px;
    }

    .control-buttons {
      margin: 15px 0;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .control-btn {
      width: 100%;
      padding: 10px 15px;
      margin: 5px 0;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .select-area-btn {
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
      box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    }

    .select-area-btn:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .button-row {
      display: flex;
      gap: 10px;
      margin: 10px 0;
    }

    .nav-btn {
      flex: 1;
      background: #6c757d;
      color: white;
    }

    .nav-btn:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }

    .restore-btn {
      background: #ffc107;
      color: #212529;
    }

    .restore-btn:hover {
      background: #e0a800;
      transform: translateY(-1px);
    }

    .control-btn:active {
      transform: translateY(0);
    }

    /* 书本列表样式 */
    .book-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .book-item {
      display: flex;
      align-items: center;
      padding: 8px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
      transition: all 0.2s;
    }

    .book-item:hover {
      background: #e9ecef;
    }

    .book-item.active {
      background: #d4edda;
      border-color: #c3e6cb;
    }

    .book-info {
      flex: 1;
      min-width: 0;
    }

    .book-title {
      font-weight: 500;
      font-size: 13px;
      color: #333;
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .book-meta {
      font-size: 11px;
      color: #666;
      display: flex;
      gap: 10px;
    }

    .book-actions {
      display: flex;
      gap: 4px;
      margin-left: 8px;
    }

    .book-btn {
      padding: 4px 8px;
      border: none;
      border-radius: 3px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .switch-btn {
      background: #007bff;
      color: white;
    }

    .switch-btn:hover {
      background: #0056b3;
    }

    .switch-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .delete-btn {
      background: #dc3545;
      color: white;
    }

    .delete-btn:hover {
      background: #c82333;
    }

    .empty-list {
      text-align: center;
      color: #666;
      font-size: 12px;
      padding: 20px;
      font-style: italic;
    }

    /* 搜索界面样式 */
    .search-container {
      margin-bottom: 15px;
    }

    .search-input-wrapper {
      display: flex;
      gap: 8px;
      margin-bottom: 10px;
    }

    .search-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      outline: none;
    }

    .search-input:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .search-btn {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background 0.2s;
    }

    .search-btn:hover {
      background: #0056b3;
    }

    .search-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .search-filters {
      display: flex;
      gap: 8px;
    }

    .filter-select {
      flex: 1;
      padding: 6px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 12px;
      background: white;
      outline: none;
    }

    .filter-select:focus {
      border-color: #007bff;
    }

    .search-results {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      background: white;
    }

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      font-size: 12px;
      font-weight: 500;
    }

    .clear-btn {
      padding: 4px 8px;
      background: #dc3545;
      color: white;
      border: none;
      border-radius: 3px;
      font-size: 11px;
      cursor: pointer;
    }

    .clear-btn:hover {
      background: #c82333;
    }

    .results-list {
      max-height: 250px;
      overflow-y: auto;
    }

    .result-item {
      padding: 10px 12px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background 0.2s;
    }

    .result-item:hover {
      background: #f8f9fa;
    }

    .result-item:last-child {
      border-bottom: none;
    }

    .result-title {
      font-weight: 500;
      font-size: 13px;
      color: #333;
      margin-bottom: 4px;
      line-height: 1.3;
    }

    .result-meta {
      font-size: 11px;
      color: #666;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .result-actions {
      display: flex;
      gap: 6px;
      margin-top: 6px;
    }

    .result-btn {
      padding: 4px 8px;
      border: none;
      border-radius: 3px;
      font-size: 10px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .download-btn {
      background: #28a745;
      color: white;
    }

    .download-btn:hover {
      background: #218838;
    }

    .download-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .preview-btn {
      background: #17a2b8;
      color: white;
    }

    .preview-btn:hover {
      background: #138496;
    }

    .search-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      gap: 10px;
      font-size: 12px;
      color: #666;
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .format-badge {
      display: inline-block;
      padding: 2px 6px;
      background: #e9ecef;
      color: #495057;
      border-radius: 3px;
      font-size: 9px;
      font-weight: 500;
      text-transform: uppercase;
      margin-right: 4px;
    }

    .source-badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 9px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .source-ylibrary {
      background: #d4edda;
      color: #155724;
    }

    .source-google {
      background: #d1ecf1;
      color: #0c5460;
    }

    .download-progress {
      margin-top: 6px;
      font-size: 10px;
      color: #666;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e9ecef;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 2px;
    }

    .progress-fill {
      height: 100%;
      background: #007bff;
      transition: width 0.3s ease;
    }

    /* 分页控件样式 */
    .pagination-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 12px;
      background: #f8f9fa;
      border-top: 1px solid #e9ecef;
      font-size: 12px;
    }

    .page-btn {
      padding: 6px 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background 0.2s;
    }

    .page-btn:hover:not(:disabled) {
      background: #0056b3;
    }

    .page-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .page-info {
      font-weight: 500;
      color: #333;
    }

    .page-size-select {
      padding: 4px 6px;
      border: 1px solid #ddd;
      border-radius: 3px;
      font-size: 11px;
      background: white;
    }

    /* 搜索源提示 */
    .source-hint {
      font-size: 10px;
      color: #666;
      margin-top: 4px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>📚 TouchFish Reader</h3>
  </div>
  
  <div class="status">
    <span class="status-text">状态: <span id="status-display">未激活</span></span>
    <button id="toggle-btn" class="toggle-btn inactive">激活</button>
  </div>
  
  <div class="control-buttons" id="control-buttons" style="display: none;">
    <button id="selectAreaBtn" class="control-btn select-area-btn">🎯 选择替换区域</button>
    <div class="button-row">
      <button id="prev-page-btn" class="control-btn nav-btn">⬅️ 上一页</button>
      <button id="next-page-btn" class="control-btn nav-btn">下一页 ➡️</button>
    </div>
    <button id="restore-btn" class="control-btn restore-btn">🔄 恢复原文</button>
  </div>
  
  <div class="progress-info" id="progress-info" style="display: none;">
    <div class="progress-item">
      <span>当前页:</span>
      <span id="current-page">0</span>
    </div>
    <div class="progress-item">
      <span>总页数:</span>
      <span id="total-pages">0</span>
    </div>
    <div class="progress-item">
      <span>进度:</span>
      <span id="progress-percent">0%</span>
    </div>
  </div>
  
  <div class="book-section">
    <div class="section-title">🔍 在线搜索电子书</div>
    <div class="search-container">
      <div class="search-input-wrapper">
        <input type="text" id="search-input" class="search-input" placeholder="输入书名或作者名...">
        <button id="search-btn" class="search-btn">搜索</button>
      </div>
      <div class="search-filters">
        <select id="source-filter" class="filter-select">
          <option value="all">所有来源</option>
          <option value="ylibrary">Ylibrary (支持下载)</option>
          <option value="googleBooks" selected>Google Books (推荐)</option>
        </select>
        <select id="format-filter" class="filter-select">
          <option value="">所有格式</option>
          <option value="pdf">PDF</option>
          <option value="epub">EPUB</option>
          <option value="txt">TXT</option>
          <option value="mobi">MOBI</option>
          <option value="azw3">AZW3</option>
        </select>
        <select id="sort-filter" class="filter-select">
          <option value="relevance">相关度</option>
          <option value="date">出版时间</option>
          <option value="size">文件大小</option>
        </select>
      </div>
      <div class="source-hint" id="source-hint">
        Google Books: 主要提供预览，部分书籍可下载
      </div>
    </div>

    <div id="search-results" class="search-results" style="display: none;">
      <div class="results-header">
        <span id="results-count">搜索结果</span>
        <button id="clear-results" class="clear-btn">清除</button>
      </div>
      <div id="results-list" class="results-list">
        <!-- 搜索结果将在这里动态生成 -->
      </div>
      <div id="pagination-controls" class="pagination-controls" style="display: none;">
        <button id="prev-page" class="page-btn" disabled>上一页</button>
        <span id="page-info" class="page-info">第 1 页</span>
        <button id="next-page" class="page-btn">下一页</button>
        <select id="page-size" class="page-size-select">
          <option value="10">10条/页</option>
          <option value="20" selected>20条/页</option>
          <option value="50">50条/页</option>
        </select>
      </div>
      <div id="search-loading" class="search-loading" style="display: none;">
        <div class="loading-spinner"></div>
        <span>正在搜索...</span>
      </div>
    </div>
  </div>

  <div class="book-section">
    <div class="section-title">📖 本地导入电子书</div>
    <div class="file-input-wrapper">
      <input type="file" id="book-file" class="file-input" accept=".txt,.epub,.pdf">
      <div class="file-input-btn">选择文件 (.txt, .epub, .pdf)</div>
    </div>
  </div>
  
  <div class="book-section" id="book-list-section" style="display: none;">
    <div class="section-title">📚 已导入书本</div>
    <div id="book-list" class="book-list">
      <!-- 书本列表将在这里动态生成 -->
    </div>
  </div>
  
  <div class="instructions">
    <h4>📋 使用说明:</h4>
    <ul>
      <li>导入txt、epub或pdf格式电子书</li>
      <li>激活插件后选择网页文本</li>
      <li>使用 Ctrl+← / Ctrl+→ 翻页</li>
      <li>右上角有隐秘的翻页按钮</li>
      <li>自动保存阅读进度</li>
      <li>📚 支持多本书籍管理和切换</li>
      <li>🗑️ 可删除不需要的书籍</li>
    </ul>
  </div>
  
  <div class="info-section">
    <div class="info-title">✨ 新功能:</div>
    <div class="info-item">• ✅ 在线搜索中文电子书</div>
    <div class="info-item">• ✅ 支持多种格式下载 (PDF, EPUB, TXT等)</div>
    <div class="info-item">• ✅ 集成Ylibrary和Google Books</div>
    <div class="info-item">• ✅ 智能搜索结果排序和筛选</div>
  </div>

  <div class="todo-section">
    <div class="todo-title">🚀 TODO (未来功能):</div>
    <div class="todo-item">• 云端同步进度</div>
    <div class="todo-item">• 阅读统计和时长记录</div>
    <div class="todo-item">• 书签和笔记功能</div>
    <div class="todo-item">• 更多电子书搜索源</div>
  </div>
  
  <script src="jszip.min.js"></script>
  <script src="pdf.min.js"></script>
  <script src="book-search-api.js"></script>
  <script src="popup.js"></script>
</body>
</html>