const express = require('express');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 代理 Liber3 搜索 API
app.post('/api/search', async (req, res) => {
    try {
        const { word } = req.body;
        
        if (!word) {
            return res.status(400).json({ error: '搜索关键词不能为空' });
        }
        
        console.log(`[搜索] 关键词: ${word}`);
        
        // 调用 Liber3 搜索 API
        const searchUrl = 'https://lgate.glitternode.ru/v1/searchV2';
        const payload = {
            address: '',
            word: word
        };
        
        const response = await fetch(searchUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`[搜索] 找到 ${data.data?.book?.length || 0} 本书`);
        
        res.json(data);
    } catch (error) {
        console.error('[搜索错误]:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 代理 Liber3 详细信息 API
app.post('/api/details', async (req, res) => {
    try {
        const { book_ids } = req.body;
        
        if (!book_ids || !Array.isArray(book_ids)) {
            return res.status(400).json({ error: '书籍ID列表不能为空' });
        }
        
        console.log(`[详情] 获取 ${book_ids.length} 本书的详细信息`);
        
        // 调用 Liber3 详细信息 API
        const detailUrl = 'https://lgate.glitternode.ru/v1/book';
        const payload = {
            book_ids: book_ids
        };
        
        const response = await fetch(detailUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`[详情] 成功获取详细信息`);
        
        res.json(data);
    } catch (error) {
        console.error('[详情错误]:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 提供静态文件
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`\n🚀 电子书搜索服务器已启动`);
    console.log(`📍 本地访问地址: http://localhost:${PORT}`);
    console.log(`🔍 搜索 API: http://localhost:${PORT}/api/search`);
    console.log(`📖 详情 API: http://localhost:${PORT}/api/details`);
    console.log(`💚 健康检查: http://localhost:${PORT}/api/health`);
    console.log(`\n请在浏览器中打开 http://localhost:${PORT} 开始使用\n`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n正在关闭服务器...');
    process.exit(0);
});