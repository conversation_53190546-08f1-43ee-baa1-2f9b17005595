# TouchFish Reader 📚

一个隐秘的Chrome浏览器插件，让你在网页上进行电子书阅读，完美融入工作环境。

## ✨ 主要功能

- 🎯 **隐秘阅读**: 选择网页文本，替换为电子书内容
- ⌨️ **快捷翻页**: 支持 `Ctrl+←` / `Ctrl+→` 快捷键翻页
- 🖱️ **隐秘按钮**: 右上角几乎不可见的翻页按钮
- 💾 **进度记忆**: 自动保存阅读进度，关闭浏览器后可继续
- 📖 **智能适配**: 根据选中文本大小自动调整显示字数
- 📚 **多格式支持**: 支持导入txt、epub和pdf格式电子书
- 🔍 **在线搜索**: 集成多个电子书搜索源，一键搜索下载
- 🌐 **多源整合**: 支持Ylibrary、Google Books等多个搜索平台
- 📱 **智能筛选**: 支持格式筛选、排序和搜索结果优化
- 📖 **多书管理**: 支持多本电子书管理和快速切换

## 🚀 安装方法

### 开发者模式安装

1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启右上角的「开发者模式」
3. 点击「加载已解压的扩展程序」
4. 选择本项目文件夹
5. 插件安装完成！

## 📋 使用说明

### 第一次使用

#### 方式一：在线搜索导入（推荐）

1. **搜索电子书**
   - 点击浏览器工具栏中的插件图标
   - 在「在线搜索电子书」区域输入书名或作者
   - 点击「搜索」按钮，等待搜索结果

2. **下载导入**
   - 浏览搜索结果，选择合适的电子书
   - 点击「下载导入」按钮
   - 等待下载和导入完成

#### 方式二：本地文件导入

1. **导入电子书**
   - 点击浏览器工具栏中的插件图标
   - 在「本地导入电子书」区域点击「选择文件」按钮
   - 选择 `.txt`、`.epub` 或 `.pdf` 格式的电子书文件
   - 等待导入完成

#### 开始阅读

1. **激活插件**
   - 在弹出窗口中点击「激活」按钮
   - 状态显示为「已激活」

2. **开始阅读**
   - 在任意网页上选中一段文本
   - 选中的文本会被替换为电子书内容
   - 文本会有轻微的背景色变化（几乎不可察觉）

### 翻页操作

- **快捷键翻页**: `Ctrl + ←` (上一页) / `Ctrl + →` (下一页)
- **鼠标翻页**: 将鼠标移到浏览器右上角，会出现隐秘的翻页按钮

### 进度管理

- 插件会自动保存你的阅读进度
- 关闭浏览器或切换网页后，再次激活时会从上次位置继续
- 在弹出窗口中可以查看当前进度

## 🎨 隐秘设计

- **低调图标**: 使用简洁的书本图标，不引人注目
- **隐形按钮**: 翻页按钮透明度极低，只有悬停时才显示
- **自然融合**: 替换的文本保持原有样式，几乎无法察觉
- **快捷操作**: 支持键盘快捷键，无需鼠标操作

## 📁 文件结构

```
touchfish-read/
├── manifest.json          # 插件配置文件
├── content.js             # 内容脚本（核心功能）
├── content.css            # 样式文件
├── popup.html             # 弹出窗口界面
├── popup.js               # 弹出窗口逻辑
├── background.js          # 后台脚本
├── icons/                 # 图标文件夹
│   ├── icon16.svg
│   ├── icon48.svg
│   └── icon128.svg
└── README.md              # 说明文档
```

## 🔧 技术特性

- **Manifest V3**: 使用最新的Chrome扩展标准
- **本地存储**: 使用Chrome Storage API保存数据
- **响应式设计**: 适配不同屏幕尺寸
- **错误处理**: 完善的异常处理机制
- **内存优化**: 高效的文本处理和存储

## 🔍 在线搜索功能

### 支持的搜索源
- **Ylibrary**: 去中心化图书馆，包含1500万+电子书
- **Google Books**: Google图书搜索，主要提供预览功能
- **更多搜索源**: 持续集成中...

### 搜索功能特性
- 🔍 **智能搜索**: 支持书名、作者名搜索
- 📊 **结果筛选**: 按格式、来源、大小筛选
- 📈 **智能排序**: 按相关度、时间、大小排序
- ⚡ **快速下载**: 支持IPFS等多种下载方式
- 🔄 **自动导入**: 下载后自动解析并导入阅读器

## 📝 支持格式

### 本地导入
- **TXT**: UTF-8编码的文本文件
- **EPUB**: 标准EPUB格式电子书
- **PDF**: 可提取文本的PDF文件

### 在线下载
- **PDF**: 最常见的电子书格式
- **EPUB**: 标准电子书格式
- **TXT**: 纯文本格式
- **MOBI**: Kindle格式（部分支持）
- **AZW3**: Kindle格式（部分支持）

## 🚀 TODO (未来功能)

- [ ] 更多电子书搜索源集成
- [ ] 云端同步进度
- [ ] 阅读统计功能
- [ ] 自定义主题
- [ ] 书签功能
- [ ] 阅读笔记
- [ ] 智能推荐系统

## ⚠️ 注意事项

1. **网络连接**: 在线搜索功能需要稳定的网络连接
2. **文件大小**: 建议电子书文件小于100MB，过大可能影响性能
3. **网页兼容**: 在某些特殊网页上可能无法正常工作
4. **隐私安全**: 所有数据都存储在本地，不会上传到服务器
5. **下载来源**: 在线下载的电子书来自公开的图书数据库
6. **版权声明**: 请确保下载的电子书符合当地法律法规

## 🐛 常见问题

**Q: 插件无法激活？**
A: 请刷新网页后重试，确保内容脚本已正确加载。

**Q: 选中文本后没有替换？**
A: 请确保已导入电子书并激活插件，选中的文本长度需要大于10个字符。

**Q: 快捷键不起作用？**
A: 请确保当前焦点在网页上，某些特殊页面可能会拦截快捷键。

**Q: 进度丢失了？**
A: 进度保存在浏览器本地存储中，清除浏览器数据会导致进度丢失。

**Q: 搜索不到结果？**
A: 请检查网络连接，尝试使用不同的关键词，或者稍后重试。

**Q: 下载失败？**
A: 可能是网络问题或书籍暂时不可用，请尝试其他书籍或稍后重试。

**Q: 下载的书籍无法打开？**
A: 请确保下载的文件格式正确，某些加密或损坏的文件可能无法正常解析。

## 📄 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**免责声明**: 本插件仅用于个人学习和娱乐，请合理使用，遵守相关法律法规。