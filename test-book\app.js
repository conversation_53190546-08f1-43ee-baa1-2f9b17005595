document.addEventListener('DOMContentLoaded', () => {
    // DOM 元素
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const loadingDiv = document.getElementById('loadingDiv');
    const messageDiv = document.getElementById('messageDiv');
    const resultsSection = document.getElementById('resultsSection');
    const resultsCount = document.getElementById('resultsCount');
    const bookGrid = document.getElementById('bookGrid');

    // 搜索表单提交事件
    searchForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const query = searchInput.value.trim();
        
        if (!query) {
            showMessage('请输入搜索关键词', 'error');
            return;
        }
        
        await searchBooks(query);
    });

    // 搜索电子书
    async function searchBooks(query) {
        // 重置界面
        resetUI();
        
        // 显示加载状态
        loadingDiv.style.display = 'block';
        searchBtn.disabled = true;
        
        try {
            // 调用 Liber3 搜索 API
            const results = await fetchLiber3Books(query);
            
            // 处理搜索结果
            displayResults(results);
        } catch (error) {
            console.error('搜索失败:', error);
            showMessage(`搜索失败: ${error.message}`, 'error');
        } finally {
            // 隐藏加载状态
            loadingDiv.style.display = 'none';
            searchBtn.disabled = false;
        }
    }

    // 调用 Liber3 API 搜索电子书
    async function fetchLiber3Books(query) {
        // 使用代理服务器解决跨域问题
        const proxyUrl = '/api/search';
        
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                word: query
            }),
            keepalive: true,
            mode: 'cors',
            cache: 'no-cache'
        });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        
        // 检查 API 返回的数据结构
        if (!data.data || !data.data.book) {
            return [];
        }
        
        // 获取搜索结果
        const books = data.data.book;
        
        // 如果有搜索结果，获取详细信息
        if (books.length > 0) {
            const bookIds = books.slice(0, 50).map(book => book.id);
            const detailedBooks = await fetchBookDetails(bookIds);
            
            // 合并搜索结果和详细信息
            return {
                searchResults: books.slice(0, 50),
                detailedBooks: detailedBooks
            };
        }
        
        return { searchResults: [], detailedBooks: {} };
    }

    // 获取电子书详细信息
    async function fetchBookDetails(bookIds) {
        const proxyUrl = '/api/details';
        
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                book_ids: bookIds
            }),
            keepalive: true,
            mode: 'cors',
            cache: 'no-cache'
        });
        
        if (!response.ok) {
            throw new Error(`获取详细信息失败: ${response.status}`);
        }
        
        const data = await response.json();
        
        // 检查 API 返回的数据结构
        if (!data.data || !data.data.book) {
            return {};
        }
        
        return data.data.book;
    }

    // 显示搜索结果
    function displayResults(results) {
        const { searchResults, detailedBooks } = results;
        
        if (!searchResults || searchResults.length === 0) {
            showMessage('未找到匹配的电子书', 'error');
            return;
        }
        
        // 显示结果数量
        resultsCount.textContent = `找到 ${searchResults.length} 本相关电子书`;
        
        // 显示结果区域
        resultsSection.style.display = 'block';
        
        // 清空之前的结果
        bookGrid.innerHTML = '';
        
        // 遍历搜索结果并创建书本卡片
        searchResults.forEach(book => {
            const bookId = book.id;
            const detail = detailedBooks[bookId]?.book || {};
            
            // 创建书本卡片
            const bookCard = document.createElement('div');
            bookCard.className = 'book-card';
            
            // 书本标题
            const titleElement = document.createElement('h3');
            titleElement.className = 'book-title';
            titleElement.textContent = book.title || '未知标题';
            
            // 书本信息
            const infoElement = document.createElement('div');
            infoElement.className = 'book-info';
            
            // 添加作者信息
            addBookInfoItem(infoElement, '作者', book.author || '未知');
            
            // 添加年份信息
            addBookInfoItem(infoElement, '年份', detail.year || '未知');
            
            // 添加出版社信息
            addBookInfoItem(infoElement, '出版社', detail.publisher || '未知');
            
            // 添加语言信息
            addBookInfoItem(infoElement, '语言', detail.language || '未知');
            
            // 添加文件大小信息
            addBookInfoItem(infoElement, '文件大小', detail.filesize || '未知');
            
            // 添加文件类型信息
            addBookInfoItem(infoElement, '文件类型', detail.extension || '未知');
            
            // 下载按钮
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'download-btn';
            downloadBtn.textContent = '下载电子书';
            downloadBtn.dataset.bookId = bookId;
            downloadBtn.addEventListener('click', () => downloadBook(bookId, book.title, detail.extension));
            
            // 将元素添加到卡片中
            bookCard.appendChild(titleElement);
            bookCard.appendChild(infoElement);
            bookCard.appendChild(downloadBtn);
            
            // 将卡片添加到网格中
            bookGrid.appendChild(bookCard);
        });
    }

    // 添加书本信息项
    function addBookInfoItem(container, label, value) {
        const item = document.createElement('div');
        item.className = 'book-info-item';
        
        const labelElement = document.createElement('div');
        labelElement.className = 'book-info-label';
        labelElement.textContent = label + ':';
        
        const valueElement = document.createElement('div');
        valueElement.className = 'book-info-value';
        valueElement.textContent = value;
        
        item.appendChild(labelElement);
        item.appendChild(valueElement);
        
        container.appendChild(item);
    }

    // 下载电子书
    async function downloadBook(bookId, title, extension) {
        try {
            showMessage('正在准备下载...', 'success');
            
            // 获取电子书详细信息
            const bookDetails = await fetchBookDetails([bookId]);
            
            if (!bookDetails || !bookDetails[bookId]) {
                throw new Error('无法获取电子书信息');
            }
            
            const bookInfo = bookDetails[bookId].book;
            const ipfsCid = bookInfo.ipfs_cid;
            
            if (!ipfsCid) {
                throw new Error('无法获取下载链接');
            }
            
            // 构造下载链接
            const fileName = title.replace(/\s+/g, '_');
            const fileExt = extension || bookInfo.extension || 'unknown';
            const downloadUrl = `https://gateway-ipfs.st/ipfs/${ipfsCid}?filename=${fileName}.${fileExt}`;
            
            // 创建下载链接并点击
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `${fileName}.${fileExt}`;
            a.target = '_blank';
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            showMessage('下载已开始，如果没有自动下载，请检查浏览器设置', 'success');
        } catch (error) {
            console.error('下载失败:', error);
            showMessage(`下载失败: ${error.message}`, 'error');
        }
    }

    // 显示消息
    function showMessage(message, type) {
        messageDiv.innerHTML = '';
        
        const messageElement = document.createElement('div');
        messageElement.className = type === 'error' ? 'error-message' : 'success-message';
        messageElement.textContent = message;
        
        messageDiv.appendChild(messageElement);
        
        // 5秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                if (messageDiv.contains(messageElement)) {
                    messageDiv.removeChild(messageElement);
                }
            }, 5000);
        }
    }

    // 重置 UI
    function resetUI() {
        messageDiv.innerHTML = '';
        resultsSection.style.display = 'none';
        bookGrid.innerHTML = '';
    }
});