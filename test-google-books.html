<!DOCTYPE html>
<html>
<head>
    <title>Google Books API测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .error { background: #ffe6e6; }
        .success { background: #e6ffe6; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Google Books API测试</h1>
    <input type="text" id="keyword" placeholder="输入搜索关键词" value="python">
    <button onclick="testGoogleBooks()">测试Google Books</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        function log(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testGoogleBooks() {
            const keyword = document.getElementById('keyword').value.trim();
            if (!keyword) {
                log('请输入搜索关键词', true);
                return;
            }
            
            log(`开始搜索: "${keyword}"`);
            
            try {
                const params = new URLSearchParams({
                    q: keyword,
                    startIndex: 0,
                    maxResults: 10,
                    printType: 'books',
                    langRestrict: 'zh'
                });
                
                const url = `https://www.googleapis.com/books/v1/volumes?${params.toString()}`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`成功! 总结果数: ${data.totalItems || 0}`);
                
                if (data.items && data.items.length > 0) {
                    log(`返回结果数: ${data.items.length}`);
                    
                    data.items.slice(0, 3).forEach((item, index) => {
                        const volumeInfo = item.volumeInfo || {};
                        const title = volumeInfo.title || '未知标题';
                        const authors = volumeInfo.authors ? volumeInfo.authors.join(', ') : '未知作者';
                        const publisher = volumeInfo.publisher || '未知出版社';
                        const year = volumeInfo.publishedDate ? volumeInfo.publishedDate.split('-')[0] : '未知年份';
                        
                        log(`${index + 1}. ${title} - ${authors} (${publisher}, ${year})`);
                    });
                } else {
                    log('没有找到结果');
                }
                
            } catch (error) {
                log(`错误: ${error.message}`, true);
                console.error('详细错误:', error);
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>
