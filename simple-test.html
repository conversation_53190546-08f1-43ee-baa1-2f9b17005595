<!DOCTYPE html>
<html>
<head>
    <title>简单API测试</title>
</head>
<body>
    <h1>简单API测试</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const result = document.getElementById('result');
            result.innerHTML = '测试中...';
            
            try {
                console.log('开始测试API...');
                
                const response = await fetch('https://api.ylibrary.org/api/search/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        keyword: 'test',
                        page: 1,
                        sensitive: false
                    })
                });
                
                console.log('响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `成功! 找到 ${data.hits} 个结果`;
                    console.log('成功:', data);
                } else {
                    const errorText = await response.text();
                    result.innerHTML = `错误: ${response.status} - ${errorText}`;
                    console.error('错误:', response.status, errorText);
                }
                
            } catch (error) {
                result.innerHTML = `异常: ${error.message}`;
                console.error('异常:', error);
            }
        }
    </script>
</body>
</html>
