<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TouchFish Reader v2.1.0 新功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .feature-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-step {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .step-number {
            font-weight: 600;
            color: #007bff;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>📚 TouchFish Reader v2.1.0 新功能测试指南</h1>
    
    <div class="success">
        <h3>🎉 版本更新亮点</h3>
        <p>v2.1.0 主要优化了搜索体验，新增了搜索源选择和分页功能，让用户可以更精准地搜索和浏览电子书。</p>
    </div>
    
    <div class="feature-section">
        <div class="feature-title">🎯 新功能1：搜索源选择</div>
        
        <div class="test-step">
            <span class="step-number">步骤1:</span> 打开TouchFish Reader插件弹出窗口
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤2:</span> 在搜索筛选器中找到"搜索源"下拉菜单
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤3:</span> 测试不同搜索源的效果：
            <ul>
                <li><strong>Ylibrary (推荐)</strong>: 默认选项，支持下载</li>
                <li><strong>Google Books</strong>: 仅预览，不支持下载</li>
                <li><strong>所有来源</strong>: 聚合搜索结果</li>
            </ul>
        </div>
        
        <div class="highlight">
            <strong>测试建议:</strong> 使用关键词"三体"分别测试不同搜索源，观察结果差异和下载按钮状态。
        </div>
        
        <table class="comparison-table">
            <tr>
                <th>搜索源</th>
                <th>书籍数量</th>
                <th>下载支持</th>
                <th>适用场景</th>
            </tr>
            <tr>
                <td>Ylibrary</td>
                <td>1500万+</td>
                <td>✅ 支持</td>
                <td>需要下载电子书</td>
            </tr>
            <tr>
                <td>Google Books</td>
                <td>数千万</td>
                <td>❌ 不支持</td>
                <td>查看书籍信息和预览</td>
            </tr>
            <tr>
                <td>所有来源</td>
                <td>聚合</td>
                <td>部分支持</td>
                <td>全面搜索</td>
            </tr>
        </table>
    </div>
    
    <div class="feature-section">
        <div class="feature-title">📄 新功能2：分页浏览</div>
        
        <div class="test-step">
            <span class="step-number">步骤1:</span> 进行一次搜索，确保有足够多的结果
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤2:</span> 在搜索结果底部找到分页控件
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤3:</span> 测试分页功能：
            <ul>
                <li>调整每页显示条数（10/20/50条）</li>
                <li>使用"上一页"/"下一页"按钮</li>
                <li>观察页面信息显示</li>
            </ul>
        </div>
        
        <div class="highlight">
            <strong>测试建议:</strong> 使用热门关键词如"小说"或"编程"来获得大量搜索结果，然后测试分页功能。
        </div>
    </div>
    
    <div class="feature-section">
        <div class="feature-title">💡 智能提示系统</div>
        
        <div class="test-step">
            <span class="step-number">步骤1:</span> 切换不同的搜索源
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤2:</span> 观察搜索框下方的提示信息变化
        </div>
        
        <div class="test-step">
            <span class="step-number">步骤3:</span> 验证提示信息的准确性：
            <ul>
                <li>Ylibrary: "支持下载，包含大量中文电子书"</li>
                <li>Google Books: "主要提供预览，部分书籍可下载"</li>
                <li>所有来源: "聚合多个搜索源的结果"</li>
            </ul>
        </div>
    </div>
    
    <div class="feature-section">
        <div class="feature-title">🔧 改进的用户体验</div>
        
        <ul class="feature-list">
            <li>筛选条件变化时自动重置到第一页</li>
            <li>搜索状态和分页状态的智能管理</li>
            <li>清除搜索结果时重置所有状态</li>
            <li>分页控件的响应式设计</li>
            <li>搜索源的智能默认选择</li>
        </ul>
    </div>
    
    <div class="feature-section">
        <div class="feature-title">📋 完整测试清单</div>
        
        <div class="test-step">
            <span class="step-number">基础功能测试:</span>
            <ul>
                <li>□ 搜索源选择器正常工作</li>
                <li>□ 默认选择Ylibrary搜索源</li>
                <li>□ 搜索源提示信息正确显示</li>
                <li>□ 不同搜索源返回不同结果</li>
            </ul>
        </div>
        
        <div class="test-step">
            <span class="step-number">分页功能测试:</span>
            <ul>
                <li>□ 分页控件在多页结果时显示</li>
                <li>□ 上一页/下一页按钮正常工作</li>
                <li>□ 页面信息正确显示</li>
                <li>□ 每页条数调整功能正常</li>
                <li>□ 筛选条件变化时重置到第一页</li>
            </ul>
        </div>
        
        <div class="test-step">
            <span class="step-number">下载功能测试:</span>
            <ul>
                <li>□ Ylibrary来源的书籍可以下载</li>
                <li>□ Google Books来源的书籍显示"不可下载"</li>
                <li>□ 下载进度正常显示</li>
                <li>□ 下载完成后自动导入</li>
            </ul>
        </div>
        
        <div class="test-step">
            <span class="step-number">界面体验测试:</span>
            <ul>
                <li>□ 搜索源切换时提示信息更新</li>
                <li>□ 分页控件布局美观</li>
                <li>□ 按钮状态正确（启用/禁用）</li>
                <li>□ 清除搜索结果功能正常</li>
            </ul>
        </div>
    </div>
    
    <div class="warning">
        <h4>⚠️ 注意事项</h4>
        <ul>
            <li>Google Books搜索源主要用于查看书籍信息，不支持直接下载</li>
            <li>建议优先使用Ylibrary搜索源以获得可下载的电子书</li>
            <li>分页功能在结果较少时（单页）会自动隐藏</li>
            <li>搜索条件变化时会自动重置到第一页</li>
        </ul>
    </div>
    
    <div class="success">
        <h4>✅ 测试完成</h4>
        <p>如果以上功能都正常工作，说明v2.1.0的新功能已成功实现。现在你可以享受更精准的搜索体验和便捷的分页浏览功能了！</p>
    </div>
    
    <div class="highlight">
        <h4>📞 反馈建议</h4>
        <p>如果在测试过程中发现任何问题或有改进建议，请记录具体的操作步骤和错误信息，以便进一步优化。</p>
    </div>
</body>
</html>
