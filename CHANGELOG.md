# TouchFish Reader 更新日志

## [2.2.0] - 2025-06-24

### 🎉 重大更新：集成Liber3 API

#### ✨ 新增功能
- **Liber3 API集成**: 新增Liber3作为主要搜索源，支持搜索和下载
- **跨域问题解决**: 通过Chrome插件的host_permissions解决跨域访问问题
- **智能搜索源**: Liber3设为默认推荐搜索源
- **IPFS下载优化**: 针对Liber3优化了IPFS网关选择

#### 🔧 技术改进
- **API架构扩展**: 在book-search-api.js中添加了完整的Liber3 API支持
- **搜索逻辑优化**: 支持Liber3的搜索和详情获取
- **下载功能增强**: 添加了从Liber3下载电子书的完整流程
- **错误处理**: 改进了API失败时的自动降级机制

#### 🎨 界面优化
- **搜索源选择**: 添加Liber3选项并设为默认
- **来源标识**: 为Liber3添加了专门的样式标识
- **提示信息**: 更新了搜索源的功能说明

#### 📚 API特性
- **搜索功能**: 支持通过关键词搜索电子书
- **详情获取**: 支持批量获取书籍详细信息
- **下载支持**: 通过IPFS CID下载电子书文件
- **格式支持**: 支持多种电子书格式

#### 🛠️ 测试工具
- **test-liber3.html**: 专门的Liber3 API测试页面
- **API状态检测**: 在测试工具中添加Liber3连接测试
- **CORS测试**: 验证跨域访问是否正常

#### 🔒 权限配置
- **host_permissions**: 添加了Liber3 API的访问权限
- **IPFS网关**: 添加了Liber3推荐的IPFS网关权限

---

## [2.1.1] - 2025-06-24

### 🚨 紧急修复和优化

#### 🐛 问题修复
- **修复搜索参数错误**: 修复了传递给搜索函数的page参数为PointerEvent对象的问题
- **API错误处理**: 改进了Ylibrary API的错误处理和重试机制
- **自动降级**: 当Ylibrary API不可用时，自动切换到Google Books

#### 🔧 稳定性改进
- **重试机制**: 添加了指数退避的重试机制，提高API调用成功率
- **错误日志**: 增强了错误日志记录，便于问题诊断
- **备用方案**: 实现了搜索源的自动切换机制

#### 🎯 用户体验优化
- **默认搜索源**: 临时将默认搜索源改为Google Books（更稳定）
- **警告消息**: 添加了warning类型的消息提示
- **状态提示**: 改进了搜索源的状态说明

#### 📋 已知问题
- **Ylibrary API**: 当前可能返回500错误，正在监控状态
- **下载功能**: 仅在Ylibrary API可用时支持下载

#### 🛠️ 调试工具
- **测试页面**: 添加了多个API测试页面
- **故障排除**: 创建了详细的故障排除指南

---

## [2.1.0] - 2025-06-24

### 🎯 搜索体验优化

#### ✨ 新增功能
- **搜索源选择**: 新增搜索源选择器，可选择Ylibrary、Google Books或所有来源
- **分页浏览**: 支持分页显示搜索结果，可调整每页条数（10/20/50条）
- **智能默认**: 默认选择可用的搜索源
- **搜索源提示**: 根据选择的搜索源显示相应的功能说明

#### 🔧 功能改进
- **搜索逻辑优化**: 根据选择的搜索源执行对应的搜索策略
- **分页控件**: 添加上一页/下一页按钮和页面信息显示
- **用户体验**: 筛选条件变化时自动重置到第一页
- **状态管理**: 改进搜索状态和分页状态的管理

#### 🎨 界面优化
- **搜索源选择器**: 在筛选器中添加搜索源选择下拉菜单
- **分页控件**: 在搜索结果底部添加分页导航
- **提示信息**: 动态显示搜索源的功能说明
- **响应式设计**: 优化分页控件的布局和样式

#### 📚 使用说明
- **Google Books**: 主要用于预览和获取书籍信息，稳定可用
- **Ylibrary**: 支持下载，但可能暂时不可用
- **分页浏览**: 支持大量搜索结果的分页浏览，提升性能

---

## [2.0.0] - 2025-06-24

### 🎉 重大更新：在线搜索功能

#### ✨ 新增功能
- **在线电子书搜索**: 集成多个电子书搜索源，支持一键搜索
- **多源整合**: 支持Ylibrary、Google Books等多个搜索平台
- **智能筛选**: 支持按格式、来源、大小筛选搜索结果
- **智能排序**: 按相关度、出版时间、文件大小排序
- **一键下载导入**: 从搜索结果直接下载并导入电子书
- **多格式支持**: 支持PDF、EPUB、TXT、MOBI、AZW3等格式下载
- **IPFS下载**: 支持通过IPFS网络下载电子书
- **下载进度显示**: 实时显示下载进度和状态
- **错误处理优化**: 完善的错误提示和重试机制

#### 🔧 技术改进
- **新增API模块**: `book-search-api.js` 专门处理电子书搜索和下载
- **网络权限配置**: 添加必要的host_permissions支持在线功能
- **异步处理优化**: 改进文件下载和处理的异步流程
- **内存管理**: 优化大文件处理，支持最大100MB文件
- **格式解析增强**: 改进EPUB和PDF文件解析能力

#### 🎨 界面优化
- **搜索界面**: 全新的搜索界面设计，包含搜索框、筛选器、结果列表
- **结果展示**: 卡片式搜索结果展示，信息丰富且操作便捷
- **加载状态**: 添加搜索和下载的加载动画和进度提示
- **用户反馈**: 改进的消息提示系统，更清晰的状态反馈
- **响应式设计**: 优化弹出窗口布局，适配更多内容

#### 📚 功能增强
- **书籍管理**: 保持原有的多本书籍管理功能
- **阅读体验**: 保持原有的隐秘阅读和翻页功能
- **进度保存**: 继续支持自动保存阅读进度
- **快捷键**: 保持原有的快捷键操作

#### 🔒 安全和隐私
- **本地存储**: 所有下载的书籍内容仍存储在本地
- **匿名搜索**: 搜索请求不包含个人标识信息
- **权限最小化**: 仅请求必要的网络访问权限

#### 📖 文档更新
- **README.md**: 更新功能介绍和使用说明
- **FEATURES.md**: 新增详细功能说明文档
- **INSTALL.md**: 新增安装和使用指南
- **测试文件**: 添加test.html和api-test.html测试页面

#### 🐛 问题修复
- 修复了某些情况下文件解析失败的问题
- 改进了错误处理机制，提供更友好的错误信息
- 优化了内存使用，减少大文件处理时的内存占用
- 修复了在某些网页上快捷键冲突的问题

#### 🚀 性能优化
- 优化了文件读取和解析速度
- 改进了搜索结果的加载和显示性能
- 减少了不必要的网络请求
- 优化了UI响应速度

---

## [1.0.0] - 2025-06-01

### 🎉 首次发布

#### ✨ 核心功能
- **隐秘阅读**: 在网页上选择文本，替换为电子书内容
- **快捷翻页**: 支持Ctrl+←/Ctrl+→快捷键翻页
- **隐秘按钮**: 右上角几乎不可见的翻页按钮
- **进度记忆**: 自动保存阅读进度
- **智能适配**: 根据选中文本大小自动调整显示字数

#### 📚 文件支持
- **TXT格式**: 支持UTF-8编码的文本文件
- **EPUB格式**: 支持标准EPUB电子书格式
- **PDF格式**: 支持可提取文本的PDF文件

#### 🎨 界面设计
- **弹出窗口**: 简洁的控制面板
- **状态显示**: 实时显示激活状态和阅读进度
- **文件导入**: 拖拽或点击选择文件导入

#### 🔧 技术特性
- **Manifest V3**: 使用最新的Chrome扩展标准
- **本地存储**: 使用Chrome Storage API保存数据
- **内容脚本**: 在网页中注入阅读功能
- **后台脚本**: 处理快捷键和系统事件

#### 🛡️ 隐私安全
- **本地存储**: 所有数据存储在本地浏览器
- **无网络请求**: 不向外部服务器发送数据
- **权限最小化**: 仅请求必要的浏览器权限

---

## 🔮 未来计划

### 即将推出 (v2.1.0)
- [ ] 更多电子书搜索源集成
- [ ] 智能推荐系统
- [ ] 书籍评分和评论功能
- [ ] 搜索历史记录

### 中期计划 (v2.2.0)
- [ ] 云端同步功能
- [ ] 多设备进度同步
- [ ] 用户账户系统
- [ ] 社交分享功能

### 长期计划 (v3.0.0)
- [ ] AI智能摘要
- [ ] 语音朗读功能
- [ ] 多语言支持
- [ ] 个性化主题
- [ ] 阅读统计分析
- [ ] 书签和笔记功能

---

## 📞 反馈和支持

如果你在使用过程中遇到问题或有改进建议，欢迎通过以下方式反馈：

- 详细描述问题现象和操作步骤
- 提供浏览器版本和操作系统信息
- 包含错误信息截图（如果有）

我们会持续改进TouchFish Reader，为用户提供更好的阅读体验！

---

## 📄 版权声明

TouchFish Reader 是一个开源项目，仅供学习和个人使用。请遵守当地法律法规，尊重版权，支持正版图书。
