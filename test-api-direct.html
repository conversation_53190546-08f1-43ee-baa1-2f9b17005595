<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API直接测试</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
        }
        
        .output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .error {
            color: #f00;
        }
        
        .success {
            color: #0f0;
        }
        
        .info {
            color: #ff0;
        }
    </style>
</head>
<body>
    <h1>API直接测试工具</h1>
    
    <div class="test-section">
        <h3>测试Ylibrary API</h3>
        <button onclick="testYlibraryDirect()">测试Ylibrary搜索</button>
        <button onclick="testCORS()">测试CORS</button>
        <button onclick="clearOutput()">清除输出</button>
    </div>
    
    <div class="output" id="output"></div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }
        
        async function testYlibraryDirect() {
            log('开始测试Ylibrary API...', 'info');
            
            const requestData = {
                keyword: 'python',
                page: 1,
                sensitive: false
            };
            
            log(`请求数据: ${JSON.stringify(requestData, null, 2)}`, 'info');
            
            try {
                const response = await fetch('https://api.ylibrary.org/api/search/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`, 'info');
                log(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`, 'info');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`错误响应: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                log(`成功! 找到 ${data.hits} 个结果`, 'success');
                log(`前3个结果:`, 'info');
                
                if (data.data && data.data.length > 0) {
                    data.data.slice(0, 3).forEach((book, index) => {
                        log(`${index + 1}. ${book.title} - ${book.author} (${book.extension})`, 'success');
                    });
                } else {
                    log('没有找到结果', 'error');
                }
                
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        async function testCORS() {
            log('测试CORS预检请求...', 'info');
            
            try {
                const response = await fetch('https://api.ylibrary.org/api/search/', {
                    method: 'OPTIONS'
                });
                
                log(`OPTIONS响应状态: ${response.status}`, 'info');
                log(`CORS头信息:`, 'info');
                
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers',
                    'Access-Control-Max-Age'
                ];
                
                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    log(`  ${header}: ${value || '未设置'}`, value ? 'success' : 'error');
                });
                
            } catch (error) {
                log(`CORS测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试API...', 'info');
        });
    </script>
</body>
</html>
