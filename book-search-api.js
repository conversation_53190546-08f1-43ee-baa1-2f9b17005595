// TouchFish Reader - 电子书搜索API模块
class BookSearchAPI {
  constructor() {
    this.apiEndpoints = {
      ylibrary: 'https://api.ylibrary.org/api',
      googleBooks: 'https://www.googleapis.com/books/v1/volumes'
    };
    
    this.requestTimeout = 10000; // 10秒超时
    this.maxRetries = 3;
  }

  // 搜索电子书（聚合多个API）
  async searchBooks(keyword, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      format = null, // 'pdf', 'epub', 'txt', 'mobi', 'azw3'
      language = 'zh', // 'zh', 'en', 'all'
      sortBy = 'relevance', // 'relevance', 'date', 'size'
      source = null // 'ylibrary', 'googleBooks', null (all)
    } = options;

    console.log(`开始搜索电子书: "${keyword}"`);

    // 如果指定了特定搜索源，只搜索该源
    if (source === 'ylibrary') {
      try {
        const result = await this.searchYlibrary(keyword, { page, pageSize, format, language });
        return {
          success: true,
          results: result.results,
          totalCount: result.totalHits,
          page,
          pageSize,
          keyword
        };
      } catch (error) {
        console.error('Ylibrary搜索失败:', error);
        return {
          success: false,
          error: error.message,
          results: [],
          totalCount: 0
        };
      }
    }

    if (source === 'googleBooks') {
      try {
        const result = await this.searchGoogleBooks(keyword, { page, pageSize, language });
        return {
          success: true,
          results: result.results,
          totalCount: result.totalHits,
          page,
          pageSize,
          keyword
        };
      } catch (error) {
        console.error('Google Books搜索失败:', error);
        return {
          success: false,
          error: error.message,
          results: [],
          totalCount: 0
        };
      }
    }

    // 搜索所有源（原有逻辑）
    const searchPromises = [];

    // Ylibrary API搜索
    searchPromises.push(
      this.searchYlibrary(keyword, { page, pageSize, format, language })
        .catch(error => {
          console.warn('Ylibrary搜索失败:', error);
          return { source: 'ylibrary', results: [], error: error.message };
        })
    );

    // Google Books API搜索
    searchPromises.push(
      this.searchGoogleBooks(keyword, { page, pageSize, language })
        .catch(error => {
          console.warn('Google Books搜索失败:', error);
          return { source: 'googleBooks', results: [], error: error.message };
        })
    );

    try {
      const searchResults = await Promise.all(searchPromises);

      // 聚合和处理结果
      const aggregatedResults = this.aggregateResults(searchResults, {
        format,
        sortBy,
        maxResults: pageSize
      });

      console.log(`搜索完成，共找到 ${aggregatedResults.length} 个结果`);

      return {
        success: true,
        results: aggregatedResults,
        totalCount: aggregatedResults.length,
        page,
        pageSize,
        keyword
      };

    } catch (error) {
      console.error('搜索失败:', error);
      return {
        success: false,
        error: error.message,
        results: [],
        totalCount: 0
      };
    }
  }

  // Ylibrary API搜索
  async searchYlibrary(keyword, options = {}) {
    const { page = 1, pageSize = 20, format = null } = options;
    
    const requestData = {
      keyword: keyword,
      page: page,
      sensitive: false
    };

    try {
      const response = await this.makeRequest(
        `${this.apiEndpoints.ylibrary}/search/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        }
      );

      if (!response.data) {
        throw new Error('Ylibrary API返回数据格式错误');
      }

      const results = response.data.map(book => ({
        id: `ylibrary_${book.id}`,
        title: book.title || '未知标题',
        author: book.author || '未知作者',
        publisher: book.publisher || '',
        isbn: book.isbn || '',
        year: book.year || '',
        format: book.extension?.toLowerCase() || 'unknown',
        fileSize: book.filesize || 0,
        source: 'ylibrary',
        sourceId: book.id,
        downloadable: true,
        description: `${book.author ? '作者: ' + book.author : ''}${book.publisher ? ' | 出版社: ' + book.publisher : ''}${book.year ? ' | 年份: ' + book.year : ''}`,
        metadata: {
          md5: book.md5,
          ipfs_cid: book.ipfs_cid,
          pages: book.pages
        }
      }));

      // 格式过滤
      const filteredResults = format 
        ? results.filter(book => book.format === format.toLowerCase())
        : results;

      return {
        source: 'ylibrary',
        results: filteredResults,
        totalHits: response.hits || filteredResults.length
      };

    } catch (error) {
      console.error('Ylibrary搜索失败:', error);
      throw new Error(`Ylibrary搜索失败: ${error.message}`);
    }
  }

  // Google Books API搜索
  async searchGoogleBooks(keyword, options = {}) {
    const { page = 1, pageSize = 20, language = 'zh' } = options;
    const startIndex = (page - 1) * pageSize;
    
    const params = new URLSearchParams({
      q: keyword,
      startIndex: startIndex,
      maxResults: Math.min(pageSize, 40), // Google Books API限制
      printType: 'books',
      langRestrict: language === 'all' ? '' : language
    });

    try {
      const response = await this.makeRequest(
        `${this.apiEndpoints.googleBooks}?${params.toString()}`
      );

      if (!response.items) {
        return {
          source: 'googleBooks',
          results: [],
          totalHits: 0
        };
      }

      const results = response.items.map(item => {
        const volumeInfo = item.volumeInfo || {};
        const accessInfo = item.accessInfo || {};
        
        return {
          id: `google_${item.id}`,
          title: volumeInfo.title || '未知标题',
          author: volumeInfo.authors ? volumeInfo.authors.join(', ') : '未知作者',
          publisher: volumeInfo.publisher || '',
          isbn: volumeInfo.industryIdentifiers ? 
            volumeInfo.industryIdentifiers.find(id => id.type === 'ISBN_13')?.identifier || 
            volumeInfo.industryIdentifiers[0]?.identifier || '' : '',
          year: volumeInfo.publishedDate ? volumeInfo.publishedDate.split('-')[0] : '',
          format: 'pdf', // Google Books主要提供PDF
          fileSize: 0, // Google Books不提供文件大小
          source: 'googleBooks',
          sourceId: item.id,
          downloadable: accessInfo.pdf?.isAvailable || accessInfo.epub?.isAvailable || false,
          description: volumeInfo.description || '',
          thumbnail: volumeInfo.imageLinks?.thumbnail || '',
          previewLink: volumeInfo.previewLink || '',
          metadata: {
            pageCount: volumeInfo.pageCount,
            categories: volumeInfo.categories,
            averageRating: volumeInfo.averageRating,
            ratingsCount: volumeInfo.ratingsCount
          }
        };
      });

      return {
        source: 'googleBooks',
        results: results,
        totalHits: response.totalItems || results.length
      };

    } catch (error) {
      console.error('Google Books搜索失败:', error);
      throw new Error(`Google Books搜索失败: ${error.message}`);
    }
  }

  // 聚合搜索结果
  aggregateResults(searchResults, options = {}) {
    const { format, sortBy = 'relevance', maxResults = 20 } = options;
    
    let allResults = [];
    
    // 合并所有搜索源的结果
    searchResults.forEach(sourceResult => {
      if (sourceResult.results && Array.isArray(sourceResult.results)) {
        allResults = allResults.concat(sourceResult.results);
      }
    });
    
    // 格式过滤
    if (format) {
      allResults = allResults.filter(book => 
        book.format && book.format.toLowerCase() === format.toLowerCase()
      );
    }
    
    // 去重（基于标题和作者）
    const uniqueResults = this.deduplicateResults(allResults);
    
    // 排序
    const sortedResults = this.sortResults(uniqueResults, sortBy);
    
    // 限制结果数量
    return sortedResults.slice(0, maxResults);
  }

  // 去重处理
  deduplicateResults(results) {
    const seen = new Set();
    return results.filter(book => {
      const key = `${book.title.toLowerCase()}_${book.author.toLowerCase()}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  // 结果排序
  sortResults(results, sortBy) {
    switch (sortBy) {
      case 'date':
        return results.sort((a, b) => {
          const yearA = parseInt(a.year) || 0;
          const yearB = parseInt(b.year) || 0;
          return yearB - yearA; // 新的在前
        });
      
      case 'size':
        return results.sort((a, b) => (b.fileSize || 0) - (a.fileSize || 0));
      
      case 'relevance':
      default:
        // 优先显示可下载的，然后按来源优先级
        return results.sort((a, b) => {
          if (a.downloadable !== b.downloadable) {
            return b.downloadable - a.downloadable;
          }
          // Ylibrary优先级更高
          if (a.source !== b.source) {
            if (a.source === 'ylibrary') return -1;
            if (b.source === 'ylibrary') return 1;
          }
          return 0;
        });
    }
  }

  // 获取书籍详细信息
  async getBookDetails(bookId, source) {
    try {
      if (source === 'ylibrary') {
        return await this.getYlibraryBookDetails(bookId);
      } else if (source === 'googleBooks') {
        return await this.getGoogleBookDetails(bookId);
      }

      throw new Error('不支持的书籍来源');
    } catch (error) {
      console.error('获取书籍详情失败:', error);
      throw error;
    }
  }

  // 获取Ylibrary书籍详情
  async getYlibraryBookDetails(bookId) {
    const requestData = {
      id: parseInt(bookId),
      source: 'zlibrary'
    };

    try {
      const response = await this.makeRequest(
        `${this.apiEndpoints.ylibrary}/detail/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData)
        }
      );

      return {
        success: true,
        details: response,
        downloadInfo: {
          available: !!response.ipfs_cid || !!response.md5,
          ipfsCid: response.ipfs_cid,
          md5: response.md5,
          downloadMethod: response.ipfs_cid ? 'ipfs' : 'magnet'
        }
      };

    } catch (error) {
      console.error('获取Ylibrary书籍详情失败:', error);
      throw new Error(`获取书籍详情失败: ${error.message}`);
    }
  }

  // 获取Google Books详情
  async getGoogleBookDetails(bookId) {
    try {
      const response = await this.makeRequest(
        `${this.apiEndpoints.googleBooks}/${bookId}`
      );

      const accessInfo = response.accessInfo || {};

      return {
        success: true,
        details: response,
        downloadInfo: {
          available: accessInfo.pdf?.isAvailable || accessInfo.epub?.isAvailable,
          pdfDownloadLink: accessInfo.pdf?.downloadLink,
          epubDownloadLink: accessInfo.epub?.downloadLink,
          downloadMethod: 'direct'
        }
      };

    } catch (error) {
      console.error('获取Google Books详情失败:', error);
      throw new Error(`获取书籍详情失败: ${error.message}`);
    }
  }

  // 下载电子书文件
  async downloadBook(bookInfo, onProgress = null) {
    try {
      console.log('开始下载电子书:', bookInfo.title);

      if (bookInfo.source === 'ylibrary') {
        return await this.downloadFromYlibrary(bookInfo, onProgress);
      } else if (bookInfo.source === 'googleBooks') {
        return await this.downloadFromGoogleBooks(bookInfo, onProgress);
      }

      throw new Error('不支持的下载源');

    } catch (error) {
      console.error('下载电子书失败:', error);
      throw error;
    }
  }

  // 从Ylibrary下载
  async downloadFromYlibrary(bookInfo, onProgress = null) {
    // 首先获取详细信息
    const details = await this.getYlibraryBookDetails(bookInfo.sourceId);

    if (!details.downloadInfo.available) {
      throw new Error('该书籍暂不支持下载');
    }

    if (details.downloadInfo.ipfsCid) {
      // 通过IPFS下载
      return await this.downloadFromIPFS(details.downloadInfo.ipfsCid, bookInfo, onProgress);
    } else {
      throw new Error('暂不支持该下载方式，请尝试其他书籍');
    }
  }

  // 从IPFS下载
  async downloadFromIPFS(ipfsCid, bookInfo, onProgress = null) {
    const ipfsGateways = [
      'https://gateway.ipfs.io/ipfs/',
      'https://ipfs.io/ipfs/',
      'https://cloudflare-ipfs.com/ipfs/'
    ];

    let lastError = null;

    for (const gateway of ipfsGateways) {
      try {
        const downloadUrl = gateway + ipfsCid;
        console.log(`尝试从IPFS网关下载: ${gateway}`);

        if (onProgress) {
          onProgress({ status: 'downloading', message: `正在从IPFS下载...`, progress: 0 });
        }

        const response = await fetch(downloadUrl);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentLength = response.headers.get('content-length');
        const total = contentLength ? parseInt(contentLength) : 0;
        let loaded = 0;

        const reader = response.body.getReader();
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          chunks.push(value);
          loaded += value.length;

          if (onProgress && total > 0) {
            const progress = Math.round((loaded / total) * 100);
            onProgress({
              status: 'downloading',
              message: `下载中... ${progress}%`,
              progress
            });
          }
        }

        const blob = new Blob(chunks);

        return {
          success: true,
          file: blob,
          filename: `${bookInfo.title}.${bookInfo.format}`,
          size: blob.size,
          format: bookInfo.format
        };

      } catch (error) {
        console.warn(`IPFS网关 ${gateway} 下载失败:`, error);
        lastError = error;
        continue;
      }
    }

    throw new Error(`所有IPFS网关都无法下载: ${lastError?.message}`);
  }

  // 从Google Books下载
  async downloadFromGoogleBooks(bookInfo, onProgress = null) {
    // Google Books通常不提供直接下载，这里主要是预览
    throw new Error('Google Books暂不支持直接下载，请尝试其他来源的书籍');
  }

  // 通用HTTP请求方法
  async makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data;
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }
      
      throw error;
    }
  }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BookSearchAPI;
} else if (typeof window !== 'undefined') {
  window.BookSearchAPI = BookSearchAPI;
}
