# TouchFish Reader 安装和使用指南

## 📦 安装步骤

### 1. 下载插件文件
确保你已经获得了完整的插件文件夹，包含以下文件：
```
touchfish-reader/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹出窗口界面
├── popup.js               # 弹出窗口逻辑
├── book-search-api.js     # 电子书搜索API模块
├── content.js             # 内容脚本
├── content.css            # 样式文件
├── background.js          # 后台脚本
├── jszip.min.js           # ZIP解压库
├── pdf.min.js             # PDF解析库
├── pdf.worker.min.js      # PDF工作线程
├── icons/                 # 图标文件夹
├── test.html              # 测试页面
└── README.md              # 说明文档
```

### 2. 在Chrome中安装插件

#### 方法一：开发者模式安装（推荐）
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 在右上角开启「开发者模式」开关
4. 点击「加载已解压的扩展程序」按钮
5. 选择 `touchfish-reader` 文件夹
6. 插件安装完成！

#### 方法二：拖拽安装
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 开启「开发者模式」
4. 将整个 `touchfish-reader` 文件夹拖拽到扩展程序页面
5. 确认安装

### 3. 验证安装
安装成功后，你应该能在Chrome工具栏看到📚图标。

## 🚀 快速开始

### 第一次使用

#### 步骤1：打开插件
点击Chrome工具栏中的📚图标，打开TouchFish Reader弹出窗口。

#### 步骤2：搜索电子书
1. 在「在线搜索电子书」区域输入书名，例如："三体"
2. 选择格式筛选（可选）
3. 点击「搜索」按钮
4. 等待搜索结果加载

#### 步骤3：下载导入
1. 浏览搜索结果，选择合适的电子书
2. 点击「下载导入」按钮
3. 等待下载和导入完成
4. 看到成功提示后，电子书已导入

#### 步骤4：激活阅读
1. 在弹出窗口中点击「激活」按钮
2. 状态显示为「已激活」

#### 步骤5：开始阅读
1. 打开任意网页（建议使用test.html测试）
2. 选中一段文本（至少10个字符）
3. 文本会被替换为电子书内容
4. 使用 `Ctrl+←` / `Ctrl+→` 翻页

## 🔍 搜索功能详解

### 搜索技巧
- **精确搜索**: 使用完整的书名获得最佳结果
- **作者搜索**: 可以搜索作者名找到相关作品
- **关键词搜索**: 使用书籍相关的关键词

### 筛选选项
- **格式筛选**: 选择PDF、EPUB、TXT等格式
- **排序方式**: 按相关度、时间、大小排序

### 搜索源说明
- **Ylibrary**: 包含大量中文电子书，下载成功率高
- **Google Books**: 主要提供预览，部分书籍可下载

## 📚 书籍管理

### 多本书籍管理
- 插件支持同时管理多本电子书
- 在「已导入书本」区域可以看到所有书籍
- 点击「切换」按钮可以切换当前阅读的书籍
- 点击「删除」按钮可以删除不需要的书籍

### 阅读进度
- 每本书的阅读进度都会自动保存
- 切换书籍时会自动恢复到上次阅读位置
- 进度信息显示在弹出窗口中

## ⌨️ 快捷键和操作

### 键盘快捷键
- `Ctrl + →`: 下一页
- `Ctrl + ←`: 上一页
- `Esc`: 退出选择模式

### 鼠标操作
- **文本选择**: 选中网页文本进行替换
- **隐秘翻页**: 鼠标移到浏览器右上角显示翻页按钮
- **弹出窗口**: 点击工具栏图标打开控制面板

### 按钮功能
- **激活/停用**: 开启或关闭阅读功能
- **选择替换区域**: 进入区域选择模式
- **恢复原文**: 恢复网页原始内容
- **上一页/下一页**: 翻页控制

## 🛠️ 故障排除

### 常见问题及解决方案

#### 插件无法安装
- 确保Chrome版本足够新（建议88+）
- 检查是否开启了开发者模式
- 确认文件夹结构完整

#### 搜索功能不工作
- 检查网络连接是否正常
- 确认插件权限已正确授予
- 尝试刷新页面后重试

#### 下载失败
- 检查网络连接稳定性
- 尝试选择其他格式的书籍
- 某些书籍可能暂时不可用

#### 文本替换不工作
- 确保已导入电子书并激活插件
- 选中的文本长度需要大于10个字符
- 某些特殊网页可能不兼容

#### 快捷键无效
- 确保当前焦点在网页上
- 某些网站可能会拦截快捷键
- 尝试点击网页空白处后再使用

### 重置插件
如果遇到严重问题，可以重置插件：
1. 在Chrome扩展程序页面找到TouchFish Reader
2. 点击「详细信息」
3. 点击「扩展程序选项」
4. 清除所有数据（如果有此选项）
5. 或者直接移除插件后重新安装

## 🔒 隐私和安全

### 数据存储
- 所有电子书内容存储在本地浏览器中
- 不会向任何服务器上传个人数据
- 阅读进度和设置保存在本地

### 网络请求
- 仅在搜索和下载时访问外部API
- 搜索请求不包含个人标识信息
- 下载通过公开的图书数据库进行

### 权限说明
插件请求的权限及用途：
- `storage`: 保存电子书和阅读进度
- `activeTab`: 在当前标签页中替换文本
- `scripting`: 注入内容脚本
- `host_permissions`: 访问电子书搜索API

## 📞 支持和反馈

### 获取帮助
- 查看README.md了解基本功能
- 查看FEATURES.md了解详细特性
- 使用test.html测试插件功能

### 反馈问题
如果遇到问题或有改进建议：
1. 详细描述问题现象
2. 提供操作步骤
3. 包含错误信息（如果有）
4. 说明浏览器版本和操作系统

### 功能建议
欢迎提出新功能建议：
- 新的电子书搜索源
- 界面改进建议
- 阅读体验优化
- 其他创新功能

---

🎉 恭喜！你已经成功安装并了解了TouchFish Reader的使用方法。开始享受隐秘的阅读体验吧！
