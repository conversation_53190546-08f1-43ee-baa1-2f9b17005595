# 电子书搜索下载网页应用

这是一个基于HTML、JavaScript和Node.js的电子书搜索下载网页应用，使用Liber3渠道进行电子书搜索和下载。

## 功能特点

- 搜索电子书：通过关键词搜索电子书
- 显示电子书信息：展示书名、作者、出版社、年份、语言、文件大小和类型等信息
- 下载电子书：一键下载所需电子书
- 响应式设计：适配不同设备屏幕大小

## 技术栈

- 前端：HTML、CSS、JavaScript（原生）
- 后端：Node.js、Express
- API代理：解决跨域问题

## 安装和使用

### 前提条件

- Node.js (>=14.0.0)
- npm 或 yarn

### 安装步骤

1. 克隆或下载本项目
2. 安装依赖

```bash
npm install
# 或
yarn install
```

3. 启动服务器

```bash
npm start
# 或
yarn start
```

4. 在浏览器中访问 `http://localhost:3000`

## 使用方法

1. 在搜索框中输入书名、作者或关键词
2. 点击"搜索"按钮
3. 浏览搜索结果
4. 点击"下载电子书"按钮下载所需电子书

## 项目结构

- `index.html` - 前端页面
- `app.js` - 前端JavaScript逻辑
- `server.js` - Node.js后端服务器
- `package.json` - 项目依赖和配置

## 开发模式

使用以下命令启动开发模式，支持自动重启服务器：

```bash
npm run dev
# 或
yarn dev
```

## 注意事项

- 本应用仅用于学习和研究目的
- 请尊重版权，不要下载和分享受版权保护的内容
- 下载的电子书仅供个人使用，不得用于商业目的