# TouchFish Reader 故障排除指南

## 🚨 当前已知问题

### Ylibrary API 连接问题

**问题描述**: Ylibrary API (https://api.ylibrary.org) 可能暂时不可用，返回500内部服务器错误。

**影响**: 
- 无法通过Ylibrary搜索和下载电子书
- 搜索功能可能失败

**临时解决方案**:
1. **使用Google Books**: 已将默认搜索源改为Google Books，可以正常搜索书籍信息
2. **本地导入**: 继续使用本地文件导入功能
3. **自动切换**: 当Ylibrary不可用时，系统会自动切换到Google Books

**状态**: 正在监控API状态，等待服务恢复

---

## 🔧 常见问题解决方案

### 1. 搜索功能不工作

**可能原因**:
- 网络连接问题
- API服务暂时不可用
- 浏览器阻止了跨域请求

**解决步骤**:
1. 检查网络连接
2. 尝试切换搜索源（Google Books通常更稳定）
3. 刷新页面重试
4. 检查浏览器控制台错误信息

### 2. 下载功能失败

**可能原因**:
- Ylibrary API不可用
- 选择的书籍不支持下载
- 网络连接不稳定

**解决步骤**:
1. 确认选择的是Ylibrary来源的书籍
2. 检查书籍是否显示"可下载"
3. 尝试选择其他格式的同一本书
4. 使用本地文件导入作为替代方案

### 3. 插件无法激活

**可能原因**:
- 内容脚本未正确加载
- 网页不兼容
- 浏览器权限问题

**解决步骤**:
1. 刷新当前网页
2. 检查插件权限设置
3. 尝试在不同网页上使用
4. 重新安装插件

### 4. 搜索结果为空

**可能原因**:
- 搜索关键词过于具体
- 选择的搜索源没有相关内容
- API返回数据格式问题

**解决步骤**:
1. 尝试更通用的关键词
2. 切换到不同的搜索源
3. 使用英文关键词搜索
4. 检查网络连接

---

## 🛠️ 调试工具

### 1. 浏览器开发者工具
打开浏览器开发者工具（F12），查看：
- **Console**: 查看JavaScript错误和日志
- **Network**: 查看API请求状态
- **Application**: 查看本地存储数据

### 2. 测试页面
使用提供的测试页面进行诊断：
- `test-api-direct.html`: 直接测试API连接
- `test-google-books.html`: 测试Google Books API
- `simple-test.html`: 简单的API测试
- `test-new-features.html`: 新功能测试指南

### 3. 日志信息
插件会在浏览器控制台输出详细的日志信息，包括：
- 搜索请求和响应
- 错误详情和堆栈跟踪
- API调用状态

---

## 📊 API状态检查

### 手动检查API状态

#### Ylibrary API
```javascript
// 在浏览器控制台运行
fetch('https://api.ylibrary.org/api/search/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ keyword: 'test', page: 1, sensitive: false })
})
.then(response => console.log('Ylibrary状态:', response.status))
.catch(error => console.error('Ylibrary错误:', error));
```

#### Google Books API
```javascript
// 在浏览器控制台运行
fetch('https://www.googleapis.com/books/v1/volumes?q=test&maxResults=1')
.then(response => console.log('Google Books状态:', response.status))
.catch(error => console.error('Google Books错误:', error));
```

---

## 🔄 恢复步骤

### 当Ylibrary API恢复时

1. **更新默认搜索源**:
   - 将默认搜索源改回Ylibrary
   - 更新提示信息

2. **测试功能**:
   - 验证搜索功能正常
   - 测试下载功能
   - 确认分页功能

3. **通知用户**:
   - 更新文档说明
   - 发布恢复公告

---

## 📞 获取帮助

### 报告问题时请提供

1. **错误信息**: 浏览器控制台的完整错误信息
2. **操作步骤**: 详细的重现步骤
3. **环境信息**: 
   - 浏览器版本
   - 操作系统
   - 插件版本
4. **网络状态**: 是否可以正常访问其他网站

### 临时替代方案

1. **使用Google Books**: 虽然不能下载，但可以搜索和预览
2. **本地文件导入**: 手动下载电子书文件后导入
3. **其他电子书源**: 寻找其他可靠的电子书下载网站

---

## 🔮 未来改进计划

1. **多API支持**: 集成更多电子书搜索API
2. **离线缓存**: 缓存搜索结果，减少API依赖
3. **健康检查**: 自动检测API状态
4. **备用方案**: 自动切换到可用的API
5. **用户通知**: 及时通知用户API状态变化

---

**最后更新**: 2025-06-24  
**状态**: Ylibrary API暂时不可用，Google Books API正常工作
