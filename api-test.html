<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TouchFish Reader API 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-container {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .test-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .test-output {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .info {
            color: #17a2b8;
        }
        
        .loading {
            color: #ffc107;
        }
        
        .result-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .result-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .result-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .result-actions {
            display: flex;
            gap: 8px;
        }
        
        .result-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
        }
        
        .download-btn {
            background: #28a745;
            color: white;
        }
        
        .details-btn {
            background: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <h1>📚 TouchFish Reader API 测试工具</h1>
    
    <div class="test-container">
        <div class="test-title">🔍 搜索测试</div>
        <input type="text" id="search-keyword" class="test-input" placeholder="输入搜索关键词，例如：三体" value="三体">
        <div>
            <label>格式筛选：</label>
            <select id="format-filter">
                <option value="">所有格式</option>
                <option value="pdf">PDF</option>
                <option value="epub">EPUB</option>
                <option value="txt">TXT</option>
                <option value="mobi">MOBI</option>
                <option value="azw3">AZW3</option>
            </select>
            
            <label style="margin-left: 20px;">排序方式：</label>
            <select id="sort-filter">
                <option value="relevance">相关度</option>
                <option value="date">出版时间</option>
                <option value="size">文件大小</option>
            </select>
        </div>
        <br>
        <button id="search-btn" class="test-button">开始搜索</button>
        <button id="clear-btn" class="test-button">清除结果</button>
        
        <div id="search-output" class="test-output" style="display: none;"></div>
        <div id="search-results" style="display: none;">
            <h4>搜索结果：</h4>
            <div id="results-container"></div>
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-title">📊 API状态测试</div>
        <button id="test-ylibrary" class="test-button">测试 Ylibrary API</button>
        <button id="test-google" class="test-button">测试 Google Books API</button>
        <button id="test-network" class="test-button">测试网络连接</button>
        
        <div id="status-output" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <div class="test-title">📖 书籍详情测试</div>
        <input type="text" id="book-id" class="test-input" placeholder="输入书籍ID">
        <select id="book-source">
            <option value="ylibrary">Ylibrary</option>
            <option value="googleBooks">Google Books</option>
        </select>
        <br>
        <button id="get-details-btn" class="test-button">获取详情</button>
        
        <div id="details-output" class="test-output" style="display: none;"></div>
    </div>

    <script src="book-search-api.js"></script>
    <script>
        // 初始化API
        const bookAPI = new BookSearchAPI();
        
        // 日志输出函数
        function log(message, type = 'info', outputId = 'search-output') {
            const output = document.getElementById(outputId);
            output.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            output.innerHTML += `<span class="${type}">${logEntry}</span>`;
            output.scrollTop = output.scrollHeight;
        }
        
        // 清除输出
        function clearOutput(outputId) {
            const output = document.getElementById(outputId);
            output.innerHTML = '';
            output.style.display = 'none';
        }
        
        // 搜索功能
        document.getElementById('search-btn').addEventListener('click', async () => {
            const keyword = document.getElementById('search-keyword').value.trim();
            const format = document.getElementById('format-filter').value;
            const sortBy = document.getElementById('sort-filter').value;
            const searchBtn = document.getElementById('search-btn');
            
            if (!keyword) {
                log('请输入搜索关键词', 'error');
                return;
            }
            
            searchBtn.disabled = true;
            searchBtn.textContent = '搜索中...';
            
            log(`开始搜索: "${keyword}"`, 'info');
            log(`筛选条件: 格式=${format || '全部'}, 排序=${sortBy}`, 'info');
            
            try {
                const options = {
                    page: 1,
                    pageSize: 10,
                    format: format || null,
                    sortBy: sortBy
                };
                
                const results = await bookAPI.searchBooks(keyword, options);
                
                if (results.success) {
                    log(`搜索成功！找到 ${results.totalCount} 个结果`, 'success');
                    displayResults(results.results);
                } else {
                    log(`搜索失败: ${results.error}`, 'error');
                }
                
            } catch (error) {
                log(`搜索异常: ${error.message}`, 'error');
                console.error('搜索错误:', error);
            } finally {
                searchBtn.disabled = false;
                searchBtn.textContent = '开始搜索';
            }
        });
        
        // 显示搜索结果
        function displayResults(results) {
            const resultsContainer = document.getElementById('results-container');
            const resultsSection = document.getElementById('search-results');
            
            resultsContainer.innerHTML = '';
            
            if (results.length === 0) {
                resultsContainer.innerHTML = '<p>没有找到相关书籍</p>';
            } else {
                results.forEach((book, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';
                    
                    const fileSizeText = book.fileSize > 0 ? formatFileSize(book.fileSize) : '未知';
                    const yearText = book.year ? ` (${book.year})` : '';
                    
                    resultDiv.innerHTML = `
                        <div class="result-title">${book.title}</div>
                        <div class="result-meta">
                            作者: ${book.author}${yearText} | 
                            格式: ${book.format.toUpperCase()} | 
                            大小: ${fileSizeText} | 
                            来源: ${book.source}
                            ${book.publisher ? `<br>出版社: ${book.publisher}` : ''}
                        </div>
                        <div class="result-actions">
                            <button class="result-btn details-btn" onclick="getBookDetails('${book.sourceId}', '${book.source}')">
                                获取详情
                            </button>
                            ${book.downloadable ? 
                                `<button class="result-btn download-btn" onclick="testDownload('${index}')">测试下载</button>` : 
                                '<span style="color: #666; font-size: 11px;">不可下载</span>'
                            }
                        </div>
                    `;
                    
                    resultsContainer.appendChild(resultDiv);
                });
            }
            
            resultsSection.style.display = 'block';
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
        
        // 获取书籍详情
        window.getBookDetails = async (bookId, source) => {
            log(`获取书籍详情: ID=${bookId}, 来源=${source}`, 'info', 'details-output');
            
            try {
                const details = await bookAPI.getBookDetails(bookId, source);
                log(`详情获取成功:`, 'success', 'details-output');
                log(JSON.stringify(details, null, 2), 'info', 'details-output');
            } catch (error) {
                log(`获取详情失败: ${error.message}`, 'error', 'details-output');
            }
        };
        
        // 测试下载
        window.testDownload = async (index) => {
            log(`开始测试下载...`, 'loading');
            // 这里只是模拟，实际下载需要在Chrome扩展环境中进行
            log(`下载测试完成（仅在扩展环境中可用）`, 'info');
        };
        
        // 清除结果
        document.getElementById('clear-btn').addEventListener('click', () => {
            clearOutput('search-output');
            document.getElementById('search-results').style.display = 'none';
        });
        
        // API状态测试
        document.getElementById('test-ylibrary').addEventListener('click', async () => {
            log('测试 Ylibrary API 连接...', 'loading', 'status-output');
            
            try {
                const testResult = await bookAPI.searchYlibrary('测试', { page: 1, pageSize: 1 });
                log('Ylibrary API 连接正常', 'success', 'status-output');
                log(`返回结果数: ${testResult.results.length}`, 'info', 'status-output');
            } catch (error) {
                log(`Ylibrary API 连接失败: ${error.message}`, 'error', 'status-output');
            }
        });
        
        document.getElementById('test-google').addEventListener('click', async () => {
            log('测试 Google Books API 连接...', 'loading', 'status-output');
            
            try {
                const testResult = await bookAPI.searchGoogleBooks('test', { page: 1, pageSize: 1 });
                log('Google Books API 连接正常', 'success', 'status-output');
                log(`返回结果数: ${testResult.results.length}`, 'info', 'status-output');
            } catch (error) {
                log(`Google Books API 连接失败: ${error.message}`, 'error', 'status-output');
            }
        });
        
        document.getElementById('test-network').addEventListener('click', async () => {
            log('测试网络连接...', 'loading', 'status-output');
            
            const testUrls = [
                'https://api.ylibrary.org',
                'https://www.googleapis.com',
                'https://gateway.ipfs.io'
            ];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
                    log(`${url} - 连接正常`, 'success', 'status-output');
                } catch (error) {
                    log(`${url} - 连接失败: ${error.message}`, 'error', 'status-output');
                }
            }
        });
        
        // 获取详情按钮
        document.getElementById('get-details-btn').addEventListener('click', async () => {
            const bookId = document.getElementById('book-id').value.trim();
            const source = document.getElementById('book-source').value;
            
            if (!bookId) {
                log('请输入书籍ID', 'error', 'details-output');
                return;
            }
            
            await getBookDetails(bookId, source);
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('API测试工具已加载', 'success');
            log('请注意：某些功能需要在Chrome扩展环境中才能正常工作', 'info');
        });
    </script>
</body>
</html>
